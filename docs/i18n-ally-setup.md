# i18n-ally 插件配置指南

## 🎯 问题解决

本文档解决了 i18n-ally 插件无法正确同步翻译到 en.json 文件的问题。

## 📋 已修复的配置

### 1. 创建了 `.i18nrc.json` 配置文件
- **位置**: `apps/web/.i18nrc.json`
- **作用**: 为 i18n-ally 插件提供项目级别的配置
- **关键配置**: 
  - 语言路径: `messages`
  - 文件匹配模式: `{locale}.json`
  - 键值样式: `nested`
  - 源语言: `zh`

### 2. 更新了 VSCode 设置
- **位置**: `.vscode/settings.json`
- **修复**: 移除了 JSON 注释（JSON 不支持注释）
- **新增配置**:
  - `i18n-ally.editor.autoSave`: 自动保存翻译
  - `i18n-ally.translate.saveAsCandidates`: 直接保存而非候选
  - `i18n-ally.extract.autoDetect`: 自动检测硬编码字符串

### 3. 增强了自定义框架配置
- **位置**: `.vscode/i18n-ally-custom-framework.yml`
- **新增**: 
  - 框架 ID 和显示名称
  - 包检测配置
  - 重构模板
  - 功能特性列表

### 4. 创建了工作区配置
- **位置**: `.vscode/i18n-ally.workspace.json`
- **作用**: 为 monorepo 环境提供专门的配置

### 5. 修复了 i18n.ts 配置
- **位置**: `apps/web/i18n.ts`
- **修复**: 支持动态语言检测而非硬编码 "en"
- **功能**: 根据浏览器语言偏好自动选择语言

## 🔧 使用方法

### 重新加载配置
1. 在 VSCode 中按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
2. 输入并选择 `i18n-ally: Reload`
3. 等待插件重新加载配置

### 验证配置
1. 检查 VSCode 状态栏是否显示 i18n-ally 图标
2. 打开任意包含翻译键的文件（如 `apps/web/components/test-i18n.tsx`）
3. 点击翻译键（如 `t('title')`），应该能跳转到翻译文件
4. 在翻译文件中编辑内容，应该能同步到所有语言文件

### 测试翻译同步
1. 打开 `apps/web/components/test-i18n.tsx`
2. 点击任意 `t('...')` 翻译键
3. 在弹出的翻译编辑器中修改翻译内容
4. 检查 `apps/web/messages/zh.json` 和 `apps/web/messages/en.json` 是否都更新了

## 🎯 关键功能

### 自动翻译同步
- ✅ 点击翻译键可直接编辑
- ✅ 编辑后自动保存到所有语言文件
- ✅ 支持嵌套键值结构
- ✅ 支持命名空间

### 硬编码检测
- ✅ 自动检测中文硬编码字符串
- ✅ 提供一键提取到翻译文件的功能
- ✅ 支持自定义正则表达式匹配

### 翻译辅助
- ✅ 支持 Google 和 DeepL 翻译引擎
- ✅ 缺失翻译自动回退
- ✅ 翻译候选建议

## 🚀 下一步

1. **重启 VSCode** 以确保所有配置生效
2. **安装 i18n-ally 插件**（如果尚未安装）
3. **运行 i18n-ally: Reload** 命令
4. **测试翻译同步功能**

## 🔍 故障排除

如果翻译同步仍然不工作：

1. 检查 VSCode 输出面板中的 i18n-ally 日志
2. 确认插件版本是最新的
3. 检查文件权限是否允许写入
4. 尝试手动重新加载 VSCode 窗口

## 📊 配置验证

运行以下命令验证配置：

```bash
cd /Users/<USER>/code/ragtop-web
node scripts/test-i18n-ally.js
```

应该看到所有配置项都显示 ✅ 状态。
