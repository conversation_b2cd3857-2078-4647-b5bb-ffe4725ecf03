#!/usr/bin/env node

/**
 * 验证 sidebar.team 翻译键
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("🔍 验证 sidebar.team 翻译键...");

try {
  // 检查英文翻译
  const enPath = path.join(__dirname, "apps/web/messages/en.json");
  const enData = JSON.parse(fs.readFileSync(enPath, "utf8"));
  const enTeam = enData.sidebar?.team;

  console.log("\n📋 英文翻译 (sidebar.team):");
  if (enTeam) {
    console.log("✅ sidebar.team 存在");
    Object.entries(enTeam).forEach(([key, value]) => {
      console.log(`  - ${key}: "${value}"`);
    });
  } else {
    console.log("❌ sidebar.team 不存在");
  }

  // 检查中文翻译
  const zhPath = path.join(__dirname, "apps/web/messages/zh.json");
  const zhData = JSON.parse(fs.readFileSync(zhPath, "utf8"));
  const zhTeam = zhData.sidebar?.team;

  console.log("\n📋 中文翻译 (sidebar.team):");
  if (zhTeam) {
    console.log("✅ sidebar.team 存在");
    Object.entries(zhTeam).forEach(([key, value]) => {
      console.log(`  - ${key}: "${value}"`);
    });
  } else {
    console.log("❌ sidebar.team 不存在");
  }

  // 检查必需的键
  const requiredKeys = [
    "admin",
    "member",
    "personalSpace",
    "enterpriseSpace",
    "personalSpaceSuffix",
    "spaceSuffix",
  ];

  console.log("\n🔍 检查必需的翻译键:");
  let allKeysExist = true;

  requiredKeys.forEach((key) => {
    const enHas = enTeam && enTeam[key];
    const zhHas = zhTeam && zhTeam[key];

    if (enHas && zhHas) {
      console.log(`✅ ${key}`);
    } else {
      console.log(`❌ ${key} - EN: ${enHas ? "存在" : "缺失"}, ZH: ${zhHas ? "存在" : "缺失"}`);
      allKeysExist = false;
    }
  });

  if (allKeysExist) {
    console.log("\n🎉 所有必需的翻译键都存在！");
    console.log("IntlError: MISSING_MESSAGE 问题已修复");
  } else {
    console.log("\n❌ 仍有缺失的翻译键");
  }
} catch (error) {
  console.error("❌ 验证失败:", error.message);
}
