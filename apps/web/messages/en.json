{"agent": {"actions": {"batchDeleteSessions": "<PERSON><PERSON> Delete Sessions", "clearSessions": "Clear Sessions", "delete": "Delete", "deleteAgent": "Delete Agent", "edit": "Edit", "editAgent": "Edit Agent"}, "batchDelete": {"cancelButton": "Cancel", "cancelText": "Cancel", "confirmButton": "Confirm Delete", "confirmText": "Confirm Delete", "deleting": "Deleting...", "description": "Are you sure you want to delete the selected {count} sessions? This action cannot be undone, and all selected sessions will be permanently deleted.", "error": "Error", "failed": "Failed to batch delete sessions", "noSelection": "Please select sessions to delete", "success": "Successfully deleted {count} sessions", "title": "Confirm Batch Delete"}, "clear": {"cancelButton": "Cancel", "cancelText": "Cancel", "clearing": "Clearing...", "confirmButton": "Confirm Clear", "confirmText": "Confirm Clear", "description": "After clearing {itemType}, all related {itemType} will be permanently deleted.", "failed": "Failed to clear sessions", "success": "Sessions cleared successfully", "title": "Confirm Clear"}, "common": {"agentList": "Agent List", "assistant": "Assistant", "cancel": "Cancel", "chat": "Cha<PERSON>", "confirm": "Confirm", "error": "Error", "failed": "Failed", "loading": "Loading...", "loadingMessage": "Loading agent information...", "modelNotConfigured": "Agent model not configured, unable to chat. Please configure a model.", "noData": "No data available", "notFound": "Agent not found", "notFoundMessage": "Specified agent not found", "success": "Success", "untitled": "Untitled Agent", "user": "User"}, "delete": {"cancelButton": "Cancel", "cancelText": "Cancel", "confirmButton": "Confirm Delete", "confirmDescription": "Are you sure you want to delete this agent? This action cannot be undone, and all related sessions and data will be permanently deleted.", "confirmText": "Delete", "confirmTitle": "Confirm Delete", "deleting": "Deleting...", "failed": "Failed to delete agent", "personalDescription": "Are you sure you want to delete this Agent? This action cannot be undone, and all related sessions and data will be permanently deleted.", "success": "Agent deleted successfully", "teamDescription": "Are you sure you want to delete this Agent? After deleting a team agent, related sessions and data of other users using this agent will also be permanently deleted."}, "details": {"actions": "Actions", "advancedSettings": "Advanced Settings", "agentDescription": "Agent Description", "agentName": "Agent Name", "agentPermission": "Agent Permission", "agentSettings": "Agent <PERSON>s", "assistantPrompt": "Assistant Prompt", "basicInfo": "Basic Information", "clearSessions": "Clear Sessions", "createTime": "Create Time", "creator": "Creator", "dataset": "Dataset", "deleteAgent": "Delete Agent", "description": "Description", "editAgent": "Edit Agent", "emptyReply": "Empty Reply", "exportSessions": "Export Sessions", "flexibility": "Flexibility", "greeting": "Greeting", "historyContext": "History Message Context (Count)", "keywordWeight": "Keyword Weight", "knowledgeBase": "Knowledge Base", "lastActiveTime": "Last Active Time", "maxTokens": "<PERSON>", "model": "Model", "modelConfig": "Model Configuration", "modelSettings": "Model Settings", "name": "Name", "noDescription": "No Description", "none": "None", "notConfigured": "Not Configured", "notSet": "Not Set", "notSetYet": "Not Set Yet", "permissions": {"personal": "Personal", "team": "Team"}, "promptSettings": "Prompt Settings", "resourceType": "Resource Type", "resourceTypes": {"databaseDataset": "Database Dataset", "fileDataset": "File Dataset", "knowledgeBase": "Knowledge Base"}, "retrievalCount": "Retrieval Count", "retrievalSettings": "Retrieval Settings", "sessionCount": "Session Count", "sessionManagement": "Session Management", "sessionSettings": "Session Settings", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "summaryPrompt": "Summary Prompt", "supportedLanguages": "Supported Languages", "systemPrompt": "System Prompt", "temperature": "Temperature", "topP": "Top P", "updateTime": "Update Time", "userPrompt": "User Prompt", "viewSessions": "View Sessions"}, "dialog": {"cancel": "Cancel", "confirm": "Confirm", "description": "Configure your dedicated assistant for your professional knowledge base here!", "save": "Save", "title": "Chat Configuration"}, "form": {"agentPermission": "Agent Permission", "assistantPrompt": "Assistant Prompt", "balanced": "Balanced", "chatModel": "Cha<PERSON>", "creative": "Creative", "database": "Database", "dataset": "Dataset", "datasetChatModel": "Dataset Chat", "datasets": "Datasets", "description": "Agent Description", "descriptionPlaceholder": "Enter a brief introduction about the Agent", "emptyReply": "Empty Reply", "emptyReplyPlaceholder": "e.g. Sorry, I cannot answer this question.", "file": "File", "flexibility": "Flexibility", "greeting": "Set Greeting", "greetingPlaceholder": "Hello! I am your assistant, how can I help you?", "historyContext": "History Message Context (Count)", "keywordWeight": "Keyword Similarity Weight", "knowledgeBase": "Knowledge Base", "maxTokens": "<PERSON>", "model": "Model", "modelNotConfiguredMessage": "Team has not configured {modelType} model, please contact administrator for configuration", "modelPlaceholder": "Please select a model", "name": "Agent Name", "namePlaceholder": "Test Agent", "personal": "Personal", "precise": "Precise", "prompt": "Prompt", "resourceType": "Resource Type", "similarityThreshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "summaryPrompt": "Summary Prompt", "summaryPromptPlaceholder": "e.g. Please summarize the dataset based on the dataset content.", "supportedLanguages": "Supported Languages", "supportedLanguagesDescription": "Select the languages supported by the Agent, at least one language must be selected", "supportedLanguagesPlaceholder": "Please select supported languages", "systemPrompt": "System Prompt", "systemPromptPlaceholder": "e.g. You are an expert in the academic field, please answer questions in as much detail as possible based on the content of the knowledge base.", "team": "Team", "temperature": "Temperature", "topN": "Top N", "topP": "Top P", "userPrompt": "User Prompt", "validation": {"datasetRequired": "Please select dataset", "descriptionRequired": "Agent description cannot be empty", "knowledgeBaseRequired": "Please select knowledge base", "languageRequired": "Please select at least one language", "modelRequired": "Please select a model", "nameRequired": "Agent name cannot be empty", "permissionRequired": "Please select Agent permission", "promptRequired": "Prompt cannot be empty", "resourceRequired": "Please select resource", "resourceTypeRequired": "Please select resource type", "systemPromptRequired": "System prompt cannot be empty"}}, "meta": {"createTime": "Created:", "creator": "Creator:", "updateTime": "Updated:", "updator": "Modified by:"}, "session": {"loadingSessionInfo": "Loading session information...", "notFound": "Session not found", "notFoundMessage": "Specified session not found", "title": "Session"}, "tabs": {"basicSettings": "Basic Settings", "modelSettings": "Model Settings", "promptEngine": "Prompt Engine"}, "tips": {"createFailed": "Failed to create agent", "deleteFailed": "Failed to delete agent", "deleteSuccess": "Agent deleted successfully", "success": "Success", "updateFailed": "Failed to update agent", "updateSuccess": "Agent updated successfully"}}, "datasets": {"addDatabaseDataset": "Add Database Dataset", "addFileDataset": "Add File Dataset", "card": {"build": "Build", "building": "Building", "buildingVersion": "Building Version:", "createTime": "Create Time:", "creator": "Creator:", "currentVersion": "Current Data Version:", "database": "Database:", "delete": "Delete", "deleteDescription": "If the dataset is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone.", "deleteTitle": "Confirm Delete", "description": "Description:", "edit": "Edit", "noPermissionDelete": "You don't have permission to delete", "none": "None"}, "comingSoon": "This feature is coming soon, please stay tuned~", "create": "Create Dataset", "databaseDataset": "Database Dataset", "delete": {"description": "If the dataset is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone.", "title": "Confirm Delete"}, "deleteDialog": {"description": "This action cannot be undone.", "multipleFiles": "Are you sure you want to delete the selected {count} files?", "singleFile": "Are you sure you want to delete file \"{fileName}\"?", "title": "Confirm Delete"}, "emptyMessage": "No datasets yet, click the button above to create your first dataset", "fileDataset": "File Dataset", "fileDetail": {"actions": "Actions", "addFiles": "Add Files", "backToDatasets": "Dataset List", "batchDelete": "<PERSON><PERSON> Delete", "batchReset": "<PERSON><PERSON>", "batchStart": "<PERSON><PERSON> Start", "batchStop": "Batch Stop", "buildDataset": "Build Dataset", "configMetadata": "Config <PERSON>", "configNote": "Data is cached locally during configuration, please do not refresh the page", "createTime2": "Create Time", "createdBy": "Created By", "deleteFile": "Delete File", "file": "File", "fileName": "File Name", "fileSize": "File Size", "hasUnsavedChanges": "Has unsaved changes", "loading": "Loading...", "metadataConfig": "Metadata Configuration", "noPermissionConfig": "You don't have permission to configure", "noPermissionDelete": "You don't have permission to delete", "notFound": "Specified file not found", "progress": "Progress", "resetParse": "Reset Parse", "saveConfig": "Save Configuration", "selectedFilesCount": "Selected {count} items", "startParse": "Start Parse", "status": "Status", "stopParse": "Stop Parse", "subtable": "Subtable", "title": "File Details", "updateTime": "Update Time", "updatedBy": "Updated By", "worksheet": "Worksheet", "worksheets": "Worksheets"}, "fileForm": {"create": "Create Dataset", "description": "Dataset Description", "descriptionPlaceholder": "Enter dataset description", "descriptionRequired": "Dataset description is required", "exampleLabel": "Example:", "name": "Dataset Name", "namePlaceholder": "Enter dataset name", "nameRequired": "Dataset name is required", "save": "Save", "supportInfo": "Only supports standard row-column structured CSV / EXCEL files. Complex format EXCEL files may fail to parse or parse successfully but query inaccurately", "update": "Update Dataset"}, "form": {"connectionError": "Connection error occurred, please try again later.", "connectionSuccess": "Connection successful! Database is accessible.", "create": "Create Database Dataset", "database": "Database Name", "databaseRequired": "Database name is required", "description": "Dataset Description", "descriptionRequired": "Dataset description is required", "mustTestFirst": "Please test the connection successfully before saving", "name": "Dataset Name", "nameRequired": "Dataset name is required", "password": "Password", "passwordRequired": "Password is required", "testConnection": "Test Connection", "testing": "Testing...", "update": "Edit Dataset", "username": "Username", "usernameRequired": "Username is required"}, "loadError": "Failed to load dataset list: {error}", "loading": "Loading...", "messages": {"buildError": "Error occurred while building dataset", "buildFailed": "Build Failed", "buildStarted": "Dataset build started", "buildSuccess": "Building", "createError": "Error occurred while creating dataset", "createFailed": "Creation Failed", "createSuccess": "Created Successfully", "databaseDatasetDeleted": "Database dataset deleted successfully", "datasetCreated": "Dataset created", "datasetUpdated": "Dataset updated", "deleteDatabaseError": "Error occurred while deleting database dataset", "deleteFailed": "Deletion Failed", "deleteFileError": "Error occurred while deleting file dataset", "deleteSuccess": "Deleted Successfully", "fileDatasetDeleted": "File dataset deleted successfully", "updateError": "Error occurred while updating dataset", "updateFailed": "Update Failed", "updateSuccess": "Updated Successfully"}, "resetDialog": {"description": "After reset, re-parsing will be required.", "multipleFiles": "Are you sure you want to reset the parse for the selected {count} files?", "singleFile": "Are you sure you want to reset the parse for file \"{fileName}\"?", "title": "Confirm Reset Parse"}, "retry": "Retry", "saveChanges": "Save Changes", "tabs": {"databaseDataset": "Database Dataset", "fileDataset": "File Dataset"}, "title": "Dataset Management", "unknownError": "Unknown error"}, "fileManagement": {"actions": "Actions", "batchParse": "<PERSON><PERSON> Parse", "fileList": "File List", "fileName": "File Name", "filesCount": "{count} files", "noFilesFound": "No matching files found", "parseButton": {"parse": "Parse", "reparse": "Re-parse"}, "parseStatus": "Parse Status", "searchPlaceholder": "Search files...", "selectedFiles": "Selected {count} files", "status": {"failed": "Parse Failed", "none": "Not Parsed", "parsing": "Parsing", "success": "Parse Success"}, "title": "File Management"}, "files": {"actions": {"cancel": "Cancel", "clearFiles": "Clear", "confirm": "Confirm", "createFolder": "Create Folder", "deleteFile": "Delete File", "moveFile": "Move File", "newItem": "New", "noPermissionDelete": "You don't have permission to delete", "renameFile": "Rename File", "retry": "Retry", "retryUpload": "Retry Upload", "selectFiles": "Select Files", "startUpload": "Start Upload", "uploadFile": "Upload File"}, "dialog": {"deleteCancel": "Cancel", "deleteConfirm": "Confirm Delete", "deleteTitle": "Delete File", "folderNameRequired": "Folder name cannot be empty", "moveTitle": "Move {type}", "nameRequired": "Name cannot be empty", "renamePlaceholder": "Enter new {type} name", "renameTitle": "Rename {type}"}, "messages": {"createFolderFailed": "Creation Failed", "createFolderFailedDesc": "Error occurred while creating folder", "createFolderSuccess": "Created Successfully", "createFolderSuccessDesc": "Folder {folderName} has been created", "deleteFailed": "Deletion Failed", "deleteFailedDesc": "Error occurred while deleting file", "deleteSuccess": "Deleted Successfully", "deleteSuccessDesc": "File has been deleted successfully", "filesSuccessCount": "{count} files uploaded successfully", "renameFolderFailed": "Rename Failed", "renameFolderFailedDesc": "Error occurred while renaming file", "renameFolderSuccess": "Renamed Successfully", "renameFolderSuccessDesc": "File has been renamed to {newName}", "uploadSuccessCount": "Uploaded {count} files successfully"}, "status": {"uploadFailed": "Upload Failed", "uploadSuccess": "Upload Success", "uploading": "Uploading", "userCancel": "User Cancel", "waitingUpload": "Waiting Upload"}, "table": {"actions": "Actions", "createTime": "Create Time", "creator": "Creator", "linkedCount": "Linked Count", "name": "Name", "size": "Size", "type": "Type", "updateTime": "Update Time", "updater": "Updater"}, "title": "File Management", "types": {"file": "File", "folder": "Folder"}, "upload": {"fileLimits": "Maximum {maxCount} files, excel, csv single file maximum {tableLimit}MB, other files single file maximum {fileLimit}MB", "selectedFiles": "Selected files ({count})", "supportedFormats": "Supported formats: {formats}", "title": "Upload Files", "uploadProgress": "Uploading {percent}%"}}, "forms": {"placeholders": {"database": "Enter database", "datasetDescription": "Enter dataset description", "datasetName": "Enter dataset name", "description": "Enter description...", "folderName": "Enter folder name", "knowledgeBaseDescription": "Enter knowledge base description", "knowledgeBaseName": "Enter knowledge base name", "password": "Enter password", "search": "Search...", "searchFiles": "Search files...", "searchMembers": "Search members...", "username": "Enter username"}, "selectOptions": {"defaultSliceMethod": "Select default document slice method", "pdfParser": "Select PDF Parser", "sliceMethod": "Select slice method"}}, "home": {"adminDescription": " As a team administrator, you can manage team members and configure models.", "description": " is an AI service centered on personal and enterprise datasets, designed to unleash the full potential of data.", "userDescription": " You can access team-shared resources and create personal resources.", "welcomeBack": "Welcome Back", "welcomeTitle": "Welcome to {siteName}"}, "knowledgeBase": {"addKnowledgeBase": "Add Knowledge Base", "addNew": "Add New Knowledge Base", "card": {"creator": "Creator:", "delete": "Delete", "description": "Description:", "edit": "Edit", "fileCount": "File Count:", "filesCount": "{count} files", "noPermissionDelete": "You don't have permission to delete"}, "chunkMethod": {"cancel": "Cancel", "currentMethod": "Current chunk method: {method}", "save": "Save", "selectNewMethod": "Select new chunk method", "title": "Chunk Method Settings"}, "create": "Create Knowledge Base", "delete": {"description": "If the knowledge base is bound to an Agent, the corresponding binding relationship will also be deleted. Are you sure you want to delete \"{name}\"? This action cannot be undone.", "title": "Confirm Delete"}, "deleteDialog": {"description": "This action cannot be undone.", "multipleFiles": "Are you sure you want to delete the selected {count} files?", "singleFile": "Are you sure you want to delete file \"{fileName}\"?", "title": "Confirm Delete"}, "details": {"actions": "Actions", "addFiles": "Add Files", "addFilesFailed": "Failed to add files, please retry", "addFilesSuccess": "Successfully added {count} files to knowledge base", "backToList": "Knowledge Base List", "batchDelete": "<PERSON><PERSON> Delete", "batchDeleteFailed": "Batch delete failed", "batchDeleteSuccess": "Batch delete successful, {count} files deleted", "batchOperationFailed": "Batch operation failed, please retry", "batchParse": "<PERSON><PERSON> Parse", "batchStart": "<PERSON><PERSON> Start", "batchStop": "Batch Stop", "batchStopSuccess": "{count} files are being cancelled", "cannotParseWhileCancelling": "Cannot parse while cancelling", "cannotSetChunkMethod": "Cannot set chunk method in current state", "chunkCount": "Chunks", "chunkMethodSetFailed": "Failed to set chunk method, please retry", "chunkMethodSetSuccess": "Chunk method set successfully", "chunkSize": "Chunk Size", "clearSelection": "Clear Selection", "createTime": "Create Time", "createTime2": "Create Time", "createdBy": "Created By", "creator": "Creator", "defaultKnowledgeBaseName": "Knowledge Base", "defaultSliceMethod": "De<PERSON><PERSON>", "deleteFile": "Delete File", "deleteFileFailed": "Failed to delete file", "deleteFileSuccess": "File deleted successfully", "documentCount": "Document Count", "documentDisabled": "Document disabled", "documentEnabled": "Document enabled", "enabled": "Enabled", "errorTitle": "Error", "fileName": "Name", "fileSize": "File Size", "filesInParseQueue": "Added {count} files to parse queue", "loading": "Loading...", "loadingError": "Failed to load knowledge base details", "noFilesToParse": "No files to parse", "noFilesToStop": "No files currently parsing", "noPermissionBatchDelete": "You don't have permission to delete these files", "noPermissionDelete": "You don't have permission to delete", "noSelection": "Please select files to operate on first", "notFound": "Specified knowledge base not found", "notSet": "Not Set", "operationFailed": "Operation failed, please retry", "parseFile": "Parse File", "progress": "Progress", "retryParse": "Retry Parse", "selectAll": "Select All", "selectedFilesCount": "Selected {count} items", "setChunkMethod": "Set <PERSON> Method", "sliceMethod": "Slice Method", "startParseSuccess": "File parsing started", "status": "Status", "stopParse": "Stop Parse", "stopParseSuccess": "File parsing stopped", "successTitle": "Success", "title": "Knowledge Base Details", "updateTime": "Update Time", "updateTime2": "Update Time", "updatedBy": "Updated By"}, "edit": "Edit Knowledge Base", "emptyMessage": "No knowledge bases available, click the button above to create your first knowledge base", "fileSelection": {"breadcrumbHome": "Home", "confirm": "Confirm Add", "description": "Please select files to add to the knowledge base", "emptyText": "No files found", "endText": "All files loaded", "loadingText": "Loading...", "rootDirectory": "Root Directory", "searchPlaceholder": "Search files...", "selectedCount": "Selected {count} files", "title": "Select files for knowledge base \"{name}\""}, "form": {"defaultSliceMethod": "Default Document Slicing Method", "description": "Knowledge Base Description", "descriptionRequired": "Knowledge base description cannot be empty", "name": "Knowledge Base Name", "nameRequired": "Knowledge base name cannot be empty", "pdfParser": "PDF Parser", "sliceMethod": "Slice Method", "sliceMethodDescription": "Select the method for document slicing", "sliceMethodRequired": "Slicing method cannot be empty", "usesDeepDoc": "Use DeepDOC to parse PDF documents"}, "loadError": "Failed to load knowledge base list: {error}", "loading": "Loading...", "messages": {"addFilesFailed": "Failed to add files", "addFilesSuccess": "Files added successfully", "batchDeleteFailed": "Batch delete failed", "batchDeleteSuccess": "Batch delete successful", "batchStartFailed": "Batch start failed", "batchStartSuccess": "Batch start successful", "batchStopFailed": "Batch stop failed", "batchStopSuccess": "Batch stop successful", "createFailed": "Failed to create knowledge base", "createSuccess": "Knowledge base created successfully", "deleteFailed": "Failed to delete knowledge base", "deleteFileFailed": "Failed to delete file", "deleteFileSuccess": "File deleted successfully", "deleteSuccess": "Knowledge base deleted successfully", "startParseFailed": "Failed to start parsing", "startParseSuccess": "Parse task started", "stopParseFailed": "Failed to stop parsing", "stopParseSuccess": "Parse task stopped", "updateFailed": "Failed to update knowledge base", "updateSuccess": "Knowledge base updated successfully"}, "operations": {"delete": "Delete", "refresh": "Refresh", "settings": "Settings", "start": "Start", "stop": "Stop"}, "retry": "Retry", "saveChanges": "Save Changes", "status": {"cancelled": "<PERSON><PERSON>ed", "cancelling": "Cancelling", "failed": "Failed", "none": "Unprocessed", "parseFailed": "Parse Failed", "parseSuccess": "Parse Success", "parsing": "Parsing", "parsingProgress": "Parsing ({percent})", "queueing": "Queueing", "success": "Success", "unknown": "Unknown Status", "unparsed": "Unparsed"}, "title": "Knowledge Base Management", "unknownError": "Unknown error"}, "model": {"cancel": "Cancel", "chatModel": "Chat Model", "confirm": "Confirm", "description": "You must select the required models before use", "embeddingModel": "Embedding Model", "nl2pythonModel": "NL2PYTHON Model", "nl2sqlModel": "NL2SQL Model", "placeholder": "Please select an appropriate model", "rerankModel": "<PERSON><PERSON>", "save": "Save", "saveFailed": "Failed to save model configuration", "saveSuccess": "Model configuration saved successfully", "saving": "Saving...", "selectChatModel": "Select Chat Model", "selectEmbeddingModel": "Select Embedding Model", "submitting": "Submitting...", "title": "Set Default Models", "validation": {"selectChatModel": "Please select an appropriate chat model", "selectEmbeddingModel": "Please select an appropriate embedding model", "selectModel": "Please select an appropriate model"}}, "password": {"cancel": "Cancel", "changeDescription": "Please enter a new password to complete the change", "changeFailed": "Failed to change password, please try again", "changeSuccess": "Password changed successfully! Dialog will close automatically...", "changeTitle": "Change Account Password", "confirm": "Confirm Change", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Re-enter new password", "newPassword": "New Password", "newPasswordPlaceholder": "Enter new password", "submitting": "Submitting...", "validation": {"confirmPasswordRequired": "Confirm password requires at least 6 characters", "newPasswordRequired": "New password requires at least 6 characters", "passwordNotMatch": "New password and confirm password do not match"}}, "session": {"loadingSessionInfo": "Loading session information...", "notFound": "Session not found", "notFoundMessage": "Specified session not found", "title": "Session"}, "sidebar": {"agent": {"addAgent": "Add Agent", "agentList": "Agent List", "createSuccess": "Agent created successfully", "myAgent": "My Agent", "teamAgent": "Team Agent"}, "projects": {"datasets": "Datasets", "files": "Files", "knowledgeBase": "Knowledge Base", "modelConfig": "Model Configuration", "teamManagement": "Team Management"}, "team": {"admin": "Admin", "enterpriseSpace": "Enterprise Space", "member": "Member", "personalSpace": "Personal Space", "personalSpaceSuffix": "'s Personal Space", "spaceSuffix": "'s Space"}, "user": {"changePassword": "Change Password", "defaultUser": "User", "logout": "Logout", "personalSpace": "Personal Space", "personalSpaceSuffix": "'s Personal Space", "spaceSuffix": "'s Space"}}, "sliceMethod": {"naive": {"description": "Default slice size is 512, users can set each file individually"}}, "team": {"accountName": "Account Name", "actions": "Actions", "active": "Active", "addMember": "Add Member", "addMembers": {"addButton": "Add Members", "description": "Select members to add to the team from existing accounts", "emptyText": "No matching members found", "endText": "Reached end of list", "loadingText": "Loading...", "searchPlaceholder": "Search members...", "selectedCount": "Selected {count} members", "title": "Add Team Members", "unknownUser": "Unknown User"}, "admin": "Admin", "delete": "Delete", "deleteMember": {"description": "Are you sure you want to delete member \"{memberName}\"? This action cannot be undone.", "title": "Confirm Delete"}, "deleteMemberTip": "Delete Member", "email": "Email", "inactive": "Inactive", "joinTime": "Join Time", "lastActive": "Last Active", "makeAdmin": "Make Admin", "member": "Member", "messages": {"addFailed": "Failed to add member", "addSuccess": "Member added successfully", "deleteFailed": "Failed to delete member", "deleteSuccess": "Member deleted successfully", "fetchFailed": "Failed to fetch available members", "removeFailed": "Failed to remove member", "removeSuccess": "Member removed successfully"}, "name": "Name", "pending": "Pending", "removeAdmin": "Remove <PERSON>", "removeMember": "Remove Member", "role": "Role", "searchPlaceholder": "Search members...", "selectedCount": "Selected {count} members", "status": "Status", "title": "Team Members", "unknownUser": "Unknown User"}, "test": {"button": {"cancel": "Cancel", "save": "Save"}, "description": "This is a component for testing i18n-ally plugin functionality.", "instructions": "Please click on translation keys in VSCode to check if you can correctly navigate to translation files and sync edits.", "note": "If you can see this English text, the translation system is working properly. Now please try editing these translation keys.", "title": "i18n-ally Test Component"}}