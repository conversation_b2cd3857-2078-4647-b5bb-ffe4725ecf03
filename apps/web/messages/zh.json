{"agent": {"actions": {"batchDeleteSessions": "批量删除Session", "clearSessions": "清空Session", "delete": "删除", "deleteAgent": "", "edit": "编辑", "editAgent": ""}, "batchDelete": {"cancelButton": "取消", "cancelText": "", "confirmButton": "确认删除", "confirmText": "", "deleting": "删除中...", "description": "您确定要删除选中的 {count} 个会话吗？删除后将无法恢复。", "error": "错误", "failed": "批量删除失败", "noSelection": "请选择要删除的会话", "success": "批量删除成功", "title": "确认批量删除"}, "clear": {"cancelButton": "取消", "cancelText": "", "clearing": "清空中...", "confirmButton": "确认清空", "confirmText": "", "description": "您确定要清空所有会话吗？清空后将无法恢复。", "failed": "清空会话失败", "success": "Session清空成功", "title": "确认清空"}, "common": {"agentList": "", "assistant": "助手", "cancel": "取消", "chat": "", "confirm": "确认", "error": "错误", "failed": "失败", "loading": "加载中...", "loadingMessage": "", "modelNotConfigured": "Agent未配置模型, 不能进行会话,请选择配置模型", "noData": "暂无数据", "notFound": "", "notFoundMessage": "", "success": "成功", "untitled": "", "user": "用户"}, "delete": {"cancelButton": "取消", "cancelText": "", "confirmButton": "确认删除", "confirmDescription": "您确定要删除Agent \"{name}\" 吗？删除后将无法恢复。", "confirmText": "", "confirmTitle": "确认删除", "deleting": "删除中...", "failed": "删除Agent失败", "personalDescription": "", "success": "Agent删除成功", "teamDescription": ""}, "details": {"actions": "操作", "advancedSettings": "高级设置", "agentDescription": "", "agentName": "", "agentPermission": "", "agentSettings": "Agent设置", "assistantPrompt": "助手提示词", "basicInfo": "基本信息", "clearSessions": "清空会话", "createTime": "创建时间", "creator": "创建人", "dataset": "数据集", "deleteAgent": "删除Agent", "description": "描述", "editAgent": "编辑Agent", "emptyReply": "", "exportSessions": "导出会话", "flexibility": "灵活性", "greeting": "", "historyContext": "", "keywordWeight": "", "knowledgeBase": "知识库", "lastActiveTime": "最后活跃时间", "maxTokens": "最大Token数", "model": "模型", "modelConfig": "", "modelSettings": "模型设置", "name": "名称", "noDescription": "", "none": "", "notConfigured": "", "notSet": "", "notSetYet": "", "permissions": {"personal": "个人", "team": "团队"}, "promptSettings": "提示词设置", "resourceType": "资源类型", "resourceTypes": {"databaseDataset": "", "fileDataset": "", "knowledgeBase": ""}, "retrievalCount": "", "retrievalSettings": "", "sessionCount": "会话数量", "sessionManagement": "会话管理", "sessionSettings": "会话设置", "similarityThreshold": "", "summaryPrompt": "", "supportedLanguages": "", "systemPrompt": "系统提示词", "temperature": "随机性", "topP": "核采样", "updateTime": "更新时间", "userPrompt": "用户提示词", "viewSessions": "查看会话"}, "dialog": {"cancel": "取消", "confirm": "确定", "description": "", "save": "保存", "title": "聊天配置"}, "form": {"agentPermission": "", "assistantPrompt": "助手提示词", "balanced": "平衡", "chatModel": "聊天", "creative": "创意", "database": "", "dataset": "数据集", "datasetChatModel": "数据集聊天", "datasets": "", "description": "Agent描述", "descriptionPlaceholder": "", "emptyReply": "", "emptyReplyPlaceholder": "", "file": "", "flexibility": "灵活性", "greeting": "", "greetingPlaceholder": "", "historyContext": "", "keywordWeight": "", "knowledgeBase": "知识库", "maxTokens": "最大Token数", "model": "模型", "modelNotConfiguredMessage": "团队没有配置{modelType}模型，请联系管理员进行配置", "modelPlaceholder": "", "name": "Agent名称", "namePlaceholder": "", "personal": "个人", "precise": "精确", "prompt": "提示词", "resourceType": "资源类型", "similarityThreshold": "", "summaryPrompt": "", "summaryPromptPlaceholder": "", "supportedLanguages": "", "supportedLanguagesDescription": "", "supportedLanguagesPlaceholder": "", "systemPrompt": "系统提示词", "systemPromptPlaceholder": "", "team": "团队", "temperature": "随机性", "topN": "", "topP": "核采样", "userPrompt": "用户提示词", "validation": {"datasetRequired": "请选择数据集", "descriptionRequired": "Agent描述不能为空", "knowledgeBaseRequired": "请选择知识库", "languageRequired": "", "modelRequired": "请选择模型", "nameRequired": "Agent名称不能为空", "permissionRequired": "请选择Agent权限", "promptRequired": "提示词不能为空", "resourceRequired": "", "resourceTypeRequired": "", "systemPromptRequired": "系统提示词不能为空"}}, "meta": {"createTime": "创建时间:", "creator": "创建人:", "updateTime": "修改时间:", "updator": "修改人:"}, "session": {"loadingSessionInfo": "加载会话信息中...", "notFound": "", "notFoundMessage": "未找到指定的会话", "title": "会话"}, "tabs": {"basicSettings": "基础设置", "modelSettings": "模型设置", "promptEngine": "提示引擎"}, "tips": {"createFailed": "创建Agent失败", "deleteFailed": "删除Agent失败", "deleteSuccess": "", "success": "成功", "updateFailed": "更新Agent失败", "updateSuccess": ""}}, "datasets": {"addDatabaseDataset": "添加数据库数据集", "addFileDataset": "添加文件数据集", "card": {"build": "构建", "building": "构建中", "buildingVersion": "构建中版本:", "createTime": "创建时间:", "creator": "创建人:", "currentVersion": "当前数据版本:", "database": "数据库:", "delete": "删除", "deleteDescription": "如果数据集已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。", "deleteTitle": "确认删除", "description": "描述:", "edit": "编辑", "noPermissionDelete": "您没有权限删除", "none": "无"}, "comingSoon": "此功能即将上线，敬请期待～", "create": "创建数据集", "databaseDataset": "数据库数据集", "delete": {"description": "如果数据集已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。", "title": "确认删除"}, "deleteDialog": {"description": "", "multipleFiles": "", "singleFile": "", "title": ""}, "emptyMessage": "暂无数据集，点击上方按钮创建第一个数据集", "fileDataset": "文件数据集", "fileDetail": {"actions": "", "addFiles": "", "backToDatasets": "", "batchDelete": "", "batchReset": "", "batchStart": "", "batchStop": "", "buildDataset": "", "configMetadata": "", "configNote": "", "createTime2": "", "createdBy": "", "deleteFile": "", "file": "", "fileName": "", "fileSize": "", "hasUnsavedChanges": "", "loading": "", "metadataConfig": "", "noPermissionConfig": "", "noPermissionDelete": "", "notFound": "", "progress": "", "resetParse": "", "saveConfig": "", "selectedFilesCount": "", "startParse": "", "status": "", "stopParse": "", "subtable": "", "title": "", "updateTime": "", "updatedBy": "", "worksheet": "", "worksheets": ""}, "fileForm": {"create": "创建数据集", "description": "数据集描述", "descriptionPlaceholder": "输入数据集描述", "descriptionRequired": "数据集描述不能为空", "exampleLabel": "示例：", "name": "数据集名称", "namePlaceholder": "输入数据集名称", "nameRequired": "数据集名称不能为空", "save": "保存", "supportInfo": "仅支持标准行列结构的CSV / EXCEL文件, 复杂格式的EXCEL文件会解析失败或解析成功查询不准确", "update": "更新数据集"}, "form": {"connectionError": "连接发生错误，请稍后重试。", "connectionSuccess": "连接成功！数据库可以正常访问。", "create": "创建数据库数据集", "database": "数据库名称", "databaseRequired": "数据库名称不能为空", "description": "数据集描述", "descriptionRequired": "数据集描述不能为空", "mustTestFirst": "请先测试连接成功后再保存", "name": "数据集名称", "nameRequired": "数据集名称不能为空", "password": "密码", "passwordRequired": "密码不能为空", "testConnection": "测试连接", "testing": "测试中...", "update": "编辑数据集", "username": "用户名", "usernameRequired": "用户名不能为空"}, "loadError": "加载数据集列表失败: {error}", "loading": "加载中...", "messages": {"buildError": "构建数据集时发生错误", "buildFailed": "构建失败", "buildStarted": "数据集已开始构建", "buildSuccess": "构建中", "createError": "创建数据集时发生错误", "createFailed": "创建失败", "createSuccess": "创建成功", "databaseDatasetDeleted": "数据库数据集已成功删除", "datasetCreated": "数据集已创建", "datasetUpdated": "数据集已更新", "deleteDatabaseError": "删除数据库数据集时发生错误", "deleteFailed": "删除失败", "deleteFileError": "删除文件数据集时发生错误", "deleteSuccess": "删除成功", "fileDatasetDeleted": "文件数据集已成功删除", "updateError": "更新数据集时发生错误", "updateFailed": "更新失败", "updateSuccess": "更新成功"}, "resetDialog": {"description": "", "multipleFiles": "", "singleFile": "", "title": ""}, "retry": "重试", "saveChanges": "保存修改", "tabs": {"databaseDataset": "数据库数据集", "fileDataset": "文件数据集"}, "title": "数据集管理", "unknownError": "未知错误"}, "fileManagement": {"actions": "", "batchParse": "", "fileList": "", "fileName": "", "filesCount": "", "noFilesFound": "", "parseButton": {"parse": "", "reparse": ""}, "parseStatus": "", "searchPlaceholder": "", "selectedFiles": "", "status": {"failed": "", "none": "", "parsing": "", "success": ""}, "title": ""}, "files": {"actions": {"cancel": "", "clearFiles": "", "confirm": "", "createFolder": "", "deleteFile": "", "moveFile": "", "newItem": "", "noPermissionDelete": "", "renameFile": "", "retry": "", "retryUpload": "", "selectFiles": "", "startUpload": "", "uploadFile": ""}, "dialog": {"deleteCancel": "", "deleteConfirm": "", "deleteTitle": "", "folderNameRequired": "", "moveTitle": "", "nameRequired": "", "renamePlaceholder": "", "renameTitle": ""}, "messages": {"createFolderFailed": "", "createFolderFailedDesc": "", "createFolderSuccess": "", "createFolderSuccessDesc": "", "deleteFailed": "", "deleteFailedDesc": "", "deleteSuccess": "", "deleteSuccessDesc": "", "filesSuccessCount": "", "renameFolderFailed": "", "renameFolderFailedDesc": "", "renameFolderSuccess": "", "renameFolderSuccessDesc": "", "uploadSuccessCount": ""}, "status": {"uploadFailed": "", "uploadSuccess": "", "uploading": "", "userCancel": "", "waitingUpload": ""}, "table": {"actions": "", "createTime": "", "creator": "", "linkedCount": "", "name": "", "size": "", "type": "", "updateTime": "", "updater": ""}, "title": "", "types": {"file": "", "folder": ""}, "upload": {"fileLimits": "", "selectedFiles": "", "supportedFormats": "", "title": "", "uploadProgress": ""}}, "forms": {"placeholders": {"database": "输入数据库", "datasetDescription": "输入数据集描述", "datasetName": "输入数据集名称", "description": "输入描述...", "folderName": "输入文件夹名称", "knowledgeBaseDescription": "输入知识库描述", "knowledgeBaseName": "输入知识库名称", "password": "输入密码", "search": "搜索...", "searchFiles": "搜索文件...", "searchMembers": "搜索成员...", "username": "输入用户名"}, "selectOptions": {"defaultSliceMethod": "", "pdfParser": "", "sliceMethod": ""}}, "home": {"adminDescription": " 作为团队管理员，您可以管理团队成员和配置模型。", "description": "是一项以个人和企业数据集为中心的人工智能服务，旨在释放数据的全部潜力。", "userDescription": " 您可以访问团队共享的资源和创建个人资源。", "welcomeBack": "欢迎回来", "welcomeTitle": "欢迎使用 {siteName}"}, "knowledgeBase": {"addKnowledgeBase": "添加知识库", "addNew": "", "card": {"creator": "", "delete": "删除", "description": "", "edit": "编辑", "fileCount": "", "filesCount": "", "noPermissionDelete": "您没有权限删除"}, "chunkMethod": {"cancel": "", "currentMethod": "", "save": "", "selectNewMethod": "", "title": ""}, "create": "创建知识库", "delete": {"description": "如果知识库已绑定Agent，对应的绑定关系也会被删除，您确定要删除 \"{name}\" 吗？此操作无法撤销。", "title": "确认删除"}, "deleteDialog": {"description": "", "multipleFiles": "", "singleFile": "", "title": ""}, "details": {"actions": "", "addFiles": "", "addFilesFailed": "", "addFilesSuccess": "", "backToList": "", "batchDelete": "", "batchDeleteFailed": "", "batchDeleteSuccess": "", "batchOperationFailed": "", "batchParse": "", "batchStart": "", "batchStop": "", "batchStopSuccess": "", "cannotParseWhileCancelling": "", "cannotSetChunkMethod": "", "chunkCount": "", "chunkMethodSetFailed": "", "chunkMethodSetSuccess": "", "chunkSize": "", "clearSelection": "", "createTime": "", "createTime2": "", "createdBy": "", "creator": "", "defaultKnowledgeBaseName": "", "defaultSliceMethod": "", "deleteFile": "", "deleteFileFailed": "", "deleteFileSuccess": "", "documentCount": "", "documentDisabled": "", "documentEnabled": "", "enabled": "", "errorTitle": "", "fileName": "", "fileSize": "", "filesInParseQueue": "", "loading": "", "loadingError": "", "noFilesToParse": "", "noFilesToStop": "", "noPermissionBatchDelete": "", "noPermissionDelete": "", "noSelection": "", "notFound": "", "notSet": "", "operationFailed": "", "parseFile": "", "progress": "", "retryParse": "", "selectAll": "", "selectedFilesCount": "", "setChunkMethod": "", "sliceMethod": "", "startParseSuccess": "", "status": "", "stopParse": "", "stopParseSuccess": "", "successTitle": "", "title": "", "updateTime": "", "updateTime2": "", "updatedBy": ""}, "edit": "编辑知识库", "emptyMessage": "暂无知识库，点击上方按钮创建第一个知识库", "fileSelection": {"breadcrumbHome": "", "confirm": "", "description": "", "emptyText": "", "endText": "", "loadingText": "", "rootDirectory": "", "searchPlaceholder": "", "selectedCount": "", "title": ""}, "form": {"defaultSliceMethod": "", "description": "知识库描述", "descriptionRequired": "知识库描述不能为空", "name": "知识库名称", "nameRequired": "知识库名称不能为空", "pdfParser": "PDF解析器", "sliceMethod": "切片方法", "sliceMethodDescription": "", "sliceMethodRequired": "", "usesDeepDoc": "使用 DeepDOC 解析 PDF 文档"}, "loadError": "加载知识库列表失败: {error}", "loading": "加载中...", "messages": {"addFilesFailed": "", "addFilesSuccess": "", "batchDeleteFailed": "", "batchDeleteSuccess": "", "batchStartFailed": "", "batchStartSuccess": "", "batchStopFailed": "", "batchStopSuccess": "", "createFailed": "", "createSuccess": "", "deleteFailed": "", "deleteFileFailed": "", "deleteFileSuccess": "", "deleteSuccess": "", "startParseFailed": "", "startParseSuccess": "", "stopParseFailed": "", "stopParseSuccess": "", "updateFailed": "", "updateSuccess": ""}, "operations": {"delete": "", "refresh": "", "settings": "", "start": "", "stop": ""}, "retry": "重试", "saveChanges": "保存更改", "status": {"cancelled": "", "cancelling": "", "failed": "", "none": "", "parseFailed": "", "parseSuccess": "", "parsing": "", "parsingProgress": "", "queueing": "", "success": "", "unknown": "", "unparsed": ""}, "title": "知识库管理", "unknownError": "未知错误"}, "model": {"cancel": "取消", "chatModel": "聊天模型", "confirm": "", "description": "", "embeddingModel": "嵌入模型", "nl2pythonModel": "", "nl2sqlModel": "", "placeholder": "", "rerankModel": "", "save": "保存", "saveFailed": "保存模型配置失败", "saveSuccess": "模型配置保存成功", "saving": "保存中...", "selectChatModel": "选择聊天模型", "selectEmbeddingModel": "选择嵌入模型", "submitting": "", "title": "设置默认模型", "validation": {"selectChatModel": "请选择适当的聊天模型", "selectEmbeddingModel": "请选择适当的嵌入模型", "selectModel": ""}}, "password": {"cancel": "取消", "changeDescription": "请输入新密码以完成修改", "changeFailed": "修改密码失败，请重试", "changeSuccess": "密码修改成功！对话框将自动关闭...", "changeTitle": "修改帐户密码", "confirm": "确认修改", "confirmPassword": "确认新密码", "confirmPasswordPlaceholder": "再次输入新密码", "newPassword": "新密码", "newPasswordPlaceholder": "输入新密码", "submitting": "提交中...", "validation": {"confirmPasswordRequired": "确认密码至少需要6个字符", "newPasswordRequired": "新密码至少需要6个字符", "passwordNotMatch": "新密码与确认密码不匹配"}}, "session": {"loadingSessionInfo": "加载会话信息中...", "notFound": "未找到指定的会话", "notFoundMessage": "未找到指定的会话", "title": "会话"}, "sidebar": {"agent": {"addAgent": "添加Agent", "agentList": "", "createSuccess": "Agent 创建成功", "myAgent": "我的Agent", "teamAgent": "团队Agent"}, "projects": {"datasets": "数据集", "files": "文件", "knowledgeBase": "知识库", "modelConfig": "模型配置", "teamManagement": "团队管理"}, "team": {"admin": "管理员", "enterpriseSpace": "企业空间", "member": "成员", "personalSpace": "个人空间", "personalSpaceSuffix": "的个人空间", "spaceSuffix": "的空间"}, "user": {"changePassword": "修改密码", "defaultUser": "用户", "logout": "退出登录", "personalSpace": "", "personalSpaceSuffix": "", "spaceSuffix": ""}}, "sliceMethod": {"naive": {"description": ""}}, "team": {"accountName": "", "actions": "操作", "active": "活跃", "addMember": "添加成员", "addMembers": {"addButton": "", "description": "", "emptyText": "", "endText": "", "loadingText": "", "searchPlaceholder": "", "selectedCount": "", "title": "", "unknownUser": ""}, "admin": "管理员", "delete": "", "deleteMember": {"description": "您确定要删除成员 \"{memberName}\" 吗？此操作无法撤销。", "title": "确认删除"}, "deleteMemberTip": "", "email": "邮箱", "inactive": "不活跃", "joinTime": "加入时间", "lastActive": "最后活跃", "makeAdmin": "设为管理员", "member": "成员", "messages": {"addFailed": "添加成员失败", "addSuccess": "成员添加成功", "deleteFailed": "", "deleteSuccess": "", "fetchFailed": "获取可添加成员失败", "removeFailed": "移除成员失败", "removeSuccess": "成员移除成功"}, "name": "姓名", "pending": "待确认", "removeAdmin": "取消管理员", "removeMember": "移除成员", "role": "角色", "searchPlaceholder": "搜索成员...", "selectedCount": "已选择 {count} 名成员", "status": "状态", "title": "团队成员", "unknownUser": "未知用户"}, "test": {"button": {"cancel": "取消", "save": "保存"}, "description": "这是一个用于测试 i18n-ally 插件功能的组件。", "instructions": "请在 VSCode 中点击翻译键，检查是否能正确跳转到翻译文件并同步编辑。", "note": "如果您能看到这个中文文本，说明翻译系统正常工作。现在请尝试编辑这些翻译键。", "title": "i18n-ally 测试组件"}}