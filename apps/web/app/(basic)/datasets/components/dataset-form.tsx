"use client";

import { type Dataset, useTestConnection } from "@/service/dataset-service";
import { zodResolver } from "@hookform/resolvers/zod";
import { Alert, AlertDescription, AlertTitle } from "@ragtop-web/ui/components/alert";
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  RequiredFormLabel,
} from "@ragtop-web/ui/components/form";
import { Input } from "@ragtop-web/ui/components/input";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 表单验证模式创建函数
const createFormSchema = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(1, t("form.nameRequired")),
    description: z.string().min(1, t("form.descriptionRequired")),
    username: z.string().min(1, t("form.usernameRequired")),
    password: z.string().min(1, t("form.passwordRequired")),
    database: z.string().min(1, t("form.databaseRequired")),
  });

type FormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface DatasetFormProps {
  dataset?: Dataset;
  onSave: (formData: FormValues) => void;
  isCreating: boolean;
  isLoading: boolean;
}

export function DatasetForm({ dataset, onSave, isCreating, isLoading }: DatasetFormProps) {
  const t = useTranslations("datasets");
  const tForm = useTranslations("forms.placeholders");
  const tCommon = useTranslations("common");
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<"success" | "error" | null>(null);
  const [testMessage, setTestMessage] = useState("");

  // 创建表单验证模式
  const formSchema = useMemo(() => createFormSchema(t), [t]);

  // 测试连接API hook
  const testConnectionMutation = useTestConnection();

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: dataset
      ? {
          name: dataset.name || "",
          description: dataset.description || "",
          username: dataset.meta?.username || "",
          password: "",
          database: dataset.meta?.database || "",
        }
      : {
          name: "",
          description: "",
          username: "",
          password: "",
          database: "",
        },
  });

  const connectFetch = (values: FormValues, onNext?: (value: FormValues) => void) => {
    testConnectionMutation.mutate(
      {
        database: values.database,
        username: values.username,
        password: values.password,
      },
      {
        onSuccess: () => {
          setTestResult("success");
          setTestMessage(t("form.connectionSuccess"));
          setIsTesting(false);
          onNext?.(values);
        },
        onError: () => {
          setTestResult("error");
          setTestMessage(t("form.connectionError"));
          setIsTesting(false);
        },
      }
    );
  };

  // 测试连接
  const handleTestConnection = async () => {
    const values = form.getValues();
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    setIsTesting(true);
    setTestResult(null);
    setTestMessage("");

    connectFetch(values);
  };

  // 保存数据集
  const handleSubmit = async () => {
    const values = form.getValues();

    // 防止重复提交
    if (isLoading) {
      return;
    }

    try {
      if (testResult !== "success") {
        connectFetch(values, (formData) => {
          onSave(formData);
        });

        return;
      }
      await onSave(values);
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("form.name")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={tForm("datasetName")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("form.description")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={tForm("datasetDescription")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="database"
          disabled={!isCreating}
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("form.database")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={tForm("database")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("form.username")}</RequiredFormLabel>
              <FormControl>
                <Input placeholder={tForm("username")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <RequiredFormLabel>{t("form.password")}</RequiredFormLabel>
              <FormControl>
                <Input type="password" placeholder={tForm("password")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {testResult && (
          <Alert variant={testResult === "success" ? "default" : "destructive"}>
            {testResult === "success" ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertTitle>
              {testResult === "success" ? t("form.connectionSuccess") : t("form.connectionFailed")}
            </AlertTitle>
            <AlertDescription>{testMessage}</AlertDescription>
          </Alert>
        )}
        <p className="text-muted-foreground text-sm">{t("form.databaseNote")}</p>

        <div className="flex gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleTestConnection}
            disabled={isTesting || isLoading}
            className="flex-1"
          >
            {isTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {t("form.testConnection")}
          </Button>

          <Button type="submit" disabled={isTesting || isLoading} className="flex-1">
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {tCommon("save")}
          </Button>
        </div>
      </form>
    </Form>
  );
}
