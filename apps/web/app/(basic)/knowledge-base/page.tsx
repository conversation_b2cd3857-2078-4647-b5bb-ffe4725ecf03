"use client";

import {
  useCreateKnowledgeBase,
  useDeleteKnowledgeBase,
  useKnowledgeBases,
  useUpdateKnowledgeBase,
  type KnowledgeBase,
  type KnowledgeBaseParams,
} from "@/service";
import { Button } from "@ragtop-web/ui/components/button";
import { Card, CardContent } from "@ragtop-web/ui/components/card";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container";
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer";
import { useToast } from "@ragtop-web/ui/components/use-toast";
import { ChevronLeft, ChevronRight, Loader2, PlusIcon, RefreshCw } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { KnowledgeBaseCard } from "./components/knowledge-base-card";
import { KnowledgeBaseForm } from "./components/knowledge-base-form";

export type ParseStatus = "success" | "failed" | "parsing" | "none";

// 本地使用的知识库类型
export type LocalKnowledgeBase = {
  id: string;
  name: string;
  parser: "DeepDOC" | "MinerU";
  sliceMethod: string;
  files: Array<{
    id: string;
    name: string;
    type: string;
    size: string;
    parseStatus: ParseStatus;
    enabled?: boolean;
  }>;
};

export default function KnowledgeBasePage() {
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("knowledgeBase");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBase | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // 使用knowledge-base-service获取知识库列表
  const {
    data: knowledgeBasesResponse,
    isLoading,
    error,
    refetch,
  } = useKnowledgeBases(currentPage, pageSize, true);

  // 从响应中提取知识库列表
  const knowledgeBases = knowledgeBasesResponse?.records || [];
  const total = knowledgeBasesResponse?.total || 0;
  const totalPages = Math.ceil(total / pageSize);

  // 知识库操作hooks
  const createKnowledgeBase = useCreateKnowledgeBase();
  const updateKnowledgeBase = useUpdateKnowledgeBase();
  const deleteKnowledgeBase = useDeleteKnowledgeBase();

  // 处理打开知识库详情页面
  const handleOpenKnowledgeBase = (knowledgeBase: KnowledgeBase) => {
    // 导航到知识库详情页面
    router.push(`/knowledge-base/${knowledgeBase.id}`);
  };

  // 处理打开创建知识库抽屉
  const handleCreateKnowledgeBase = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedKnowledgeBase(null);
    setIsCreating(true);
    setIsDrawerOpen(true);
  };

  // 处理关闭抽屉
  const handleCloseDrawer = () => {
    setIsDrawerOpen(false);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 处理保存知识库
  const handleSaveKnowledgeBase = (knowledgeBaseData: KnowledgeBaseParams) => {
    if (isCreating) {
      // 创建新知识库
      createKnowledgeBase.mutate(knowledgeBaseData, {
        onSuccess: () => {
          toast({
            title: t("messages.createSuccess"),
            description: `知识库已创建`,
          });
          setIsDrawerOpen(false);
          // 创建成功后刷新列表，useKnowledgeBases会自动通过queryClient.invalidateQueries刷新
        },
        onError: () => {
          toast({
            title: t("messages.createFailed"),
            description: "创建知识库时发生错误",
            variant: "destructive",
          });
        },
      });
    } else if (selectedKnowledgeBase) {
      // 更新现有知识库
      updateKnowledgeBase.mutate(
        {
          ...knowledgeBaseData,
          kbase_id: selectedKnowledgeBase.id,
        },
        {
          onSuccess: () => {
            toast({
              title: t("messages.updateSuccess"),
              description: `知识库 ${knowledgeBaseData.name} 已更新`,
            });
            setIsDrawerOpen(false);
            // 更新成功后刷新列表，useKnowledgeBases会自动通过queryClient.invalidateQueries刷新
          },
          onError: () => {
            toast({
              title: t("messages.updateFailed"),
              description: "更新知识库时发生错误",
              variant: "destructive",
            });
          },
        }
      );
    }
  };

  // 处理删除知识库
  const handleDeleteKnowledgeBase = (kbaseId: string) => {
    deleteKnowledgeBase.mutate(
      { kbase_id: kbaseId },
      {
        onSuccess: () => {
          toast({
            title: "删除成功",
            description: "知识库已成功删除",
          });
        },
        onError: () => {
          toast({
            title: "删除失败",
            description: "删除知识库时发生错误",
            variant: "destructive",
          });
        },
      }
    );
  };

  // 处理知识库编辑

  const handleEdit = (knowledgeBaseData: KnowledgeBase, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedKnowledgeBase(knowledgeBaseData);
    setIsCreating(false);
    setIsDrawerOpen(true);
  };

  return (
    <CustomContainer title={t("title")}>
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" strokeWidth={1.5} />
          <span className="ml-2">{t("loading")}</span>
        </div>
      ) : error ? (
        <div className="py-12 text-center">
          <div className="text-destructive mb-4">
            {t("loadError", { error: error.message || t("unknownError") })}
          </div>
          <Button variant="outline" onClick={() => refetch()} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            {t("retry")}
          </Button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* 添加知识库卡片 */}
            <Card
              className="hover:border-primary/50 cursor-pointer border-dashed transition-colors"
              onClick={handleCreateKnowledgeBase}
            >
              <CardContent className="flex items-center justify-center">
                <div className="text-muted-foreground flex flex-col items-center">
                  <PlusIcon className="mb-2 h-10 w-10" />
                  <span className="text-md">{t("addNew")}</span>
                </div>
              </CardContent>
            </Card>

            {/* 知识库卡片列表 */}
            {knowledgeBases.map((knowledgeBase) => (
              <KnowledgeBaseCard
                key={knowledgeBase.id}
                knowledgeBase={knowledgeBase}
                onClick={() => handleOpenKnowledgeBase(knowledgeBase)}
                onDelete={() => handleDeleteKnowledgeBase(knowledgeBase.id)}
                onEdit={handleEdit}
              />
            ))}
          </div>

          {/* 分页控制 */}
          {totalPages > 1 && (
            <div className="mt-8 flex items-center justify-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>

              <div className="flex items-center gap-2">
                <span className="text-muted-foreground text-sm">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <span className="text-muted-foreground text-sm">(共 {total} 条记录)</span>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="flex items-center gap-2"
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* 空状态 */}
          {!isLoading && knowledgeBases.length === 0 && (
            <div className="text-muted-foreground py-12 text-center">
              暂无知识库，点击上方按钮创建第一个知识库
            </div>
          )}
        </>
      )}

      {/* 知识库抽屉 */}
      <CustomDrawer
        open={isDrawerOpen}
        onClose={handleCloseDrawer}
        title={isCreating ? t("create") : t("edit")}
      >
        {isDrawerOpen && (
          <KnowledgeBaseForm
            knowledgeBase={isCreating ? undefined : selectedKnowledgeBase}
            onSave={(formData) => {
              handleSaveKnowledgeBase(formData);
            }}
            isLoading={createKnowledgeBase?.isPending || updateKnowledgeBase?.isPending}
            isCreating={isCreating}
          />
        )}
      </CustomDrawer>
    </CustomContainer>
  );
}
