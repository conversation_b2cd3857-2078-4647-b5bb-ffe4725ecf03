{"localesPaths": ["messages"], "pathMatcher": "{locale}.json", "keystyle": "nested", "sourceLanguage": "zh", "displayLanguage": "zh", "enabledFrameworks": ["next-intl"], "sortKeys": true, "namespace": true, "extract": {"keygeneration": "camelCase", "keyMaxLength": 100, "autoDetect": true, "includeSubDirs": true, "parsers": {"tsx": "typescript", "ts": "typescript", "jsx": "javascript", "js": "javascript"}}, "review": {"enabled": true, "gutters": true, "removedId": true}, "hardcodedStrings": {"enabled": true, "regex": ["['\"`]([\\u4e00-\\u9fff][^'\"`;]*)['\"`]", "(?:title|label|placeholder|alt|aria-label|content|description)\\s*[=:]\\s*['\"`]([\\u4e00-\\u9fff][^'\"`;]*)['\"`]", ">([\\u4e00-\\u9fff][^<]*)<", "\\{\\s*['\"`]([\\u4e00-\\u9fff][^'\"`;]*)['\"`]\\s*\\}"], "ignoreRegex": ["^[a-zA-Z0-9_\\-\\.]+$", "^\\d+(\\.\\d+)?$", "^[a-zA-Z0-9_\\-\\.@]+\\.[a-zA-Z]{2,}$", "^#[0-9a-fA-F]{3,8}$"]}, "usage": {"scanningDirs": ["app", "components", "lib", "hooks"]}}