/**
 * i18n-ally 插件配置文件
 * 用于确保翻译同步正常工作
 */

module.exports = {
  // 语言配置
  locales: ['zh', 'en'],
  sourceLanguage: 'zh',
  displayLanguage: 'zh',
  
  // 路径配置
  localesPaths: ['apps/web/messages'],
  pathMatcher: '{locale}.json',
  
  // 键值样式
  keystyle: 'nested',
  namespace: true,
  
  // 框架支持
  enabledFrameworks: ['next-intl'],
  
  // 编辑器配置
  editor: {
    preferEditor: true,
    autoSave: true,
    autoSaveDelay: 1000
  },
  
  // 翻译引擎
  translate: {
    engines: ['google', 'deepl'],
    fallbackMissing: true
  },
  
  // 解析器配置
  parsers: {
    typescript: {
      enabled: true
    },
    javascript: {
      enabled: true
    },
    json: {
      enabled: true
    }
  },
  
  // 使用模式匹配
  regex: {
    usageMatchAppend: [
      // 匹配 useTranslations('namespace').t('key')
      "(?:useTranslations|getTranslations)\\s*\\(\\s*['\"`]([^'\"`.]+(?:\\.[^'\"`.]+)*)['\"`]\\s*\\).*?\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)",
      // 匹配 t('key') 在 useTranslations 上下文中
      "\\bt\\s*\\(\\s*['\"`]([^'\"`.]+)['\"`]\\s*\\)"
    ],
    key: "(?:['\"`])([\\w\\d\\-_\\.]+)(?:['\"`])"
  },
  
  // 提取配置
  extract: {
    keyMaxLength: 100,
    autoDetect: true,
    keygeneration: 'camelCase'
  },
  
  // 保持配置
  keepFulfilled: true,
  keepFulfilledPlaceholders: true,
  
  // 排序配置
  sortKeys: true,
  
  // 格式化配置
  indent: 2,
  tabStyle: 'space',
  
  // 重新加载配置
  fullReloadOnChanged: true,
  
  // 检测配置
  detection: {
    autoDetection: true
  },
  
  // 使用配置
  usage: {
    scanning: 'active',
    preferredDelimiter: '.',
    preferredLanguage: 'zh'
  }
};
